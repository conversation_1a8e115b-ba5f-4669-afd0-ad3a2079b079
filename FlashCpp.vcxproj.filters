﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
    <Filter Include="Header Files\coffi">
      <UniqueIdentifier>{62ba297c-8034-4e35-8a12-bc5d3186ac0f}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Parser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\benchmark.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AstNodeTypes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ChunkedAnyVector.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tests\FlashCppTest\FlashCppTest\FlashCppTest\FlashCppTest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LibClangIRGenerator_benchmark.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CodeViewDebug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\CommandLineParser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FileReader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FileTree.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\CompileContext.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\Lexer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\AstNodeTypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\Parser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\Token.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\CodeGen.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\IRTypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ObjFileWriter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\coffi\coffi.hpp">
      <Filter>Header Files\coffi</Filter>
    </ClInclude>
    <ClInclude Include="src\coffi\coffi_directory.hpp">
      <Filter>Header Files\coffi</Filter>
    </ClInclude>
    <ClInclude Include="src\coffi\coffi_headers.hpp">
      <Filter>Header Files\coffi</Filter>
    </ClInclude>
    <ClInclude Include="src\coffi\coffi_relocation.hpp">
      <Filter>Header Files\coffi</Filter>
    </ClInclude>
    <ClInclude Include="src\coffi\coffi_section.hpp">
      <Filter>Header Files\coffi</Filter>
    </ClInclude>
    <ClInclude Include="src\coffi\coffi_strings.hpp">
      <Filter>Header Files\coffi</Filter>
    </ClInclude>
    <ClInclude Include="src\coffi\coffi_symbols.hpp">
      <Filter>Header Files\coffi</Filter>
    </ClInclude>
    <ClInclude Include="src\coffi\coffi_types.hpp">
      <Filter>Header Files\coffi</Filter>
    </ClInclude>
    <ClInclude Include="src\coffi\coffi_utils.hpp">
      <Filter>Header Files\coffi</Filter>
    </ClInclude>
    <ClInclude Include="src\IRConverter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SymbolTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ChunkedAnyVector.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="LICENSE" />
    <None Include="tests\.runsettings">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
</Project>