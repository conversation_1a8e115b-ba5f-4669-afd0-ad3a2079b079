{"version": "0.2.0", "configurations": [{"name": "Run Tests", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/x64/test", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false}, {"name": "Launch Benchmark", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/x64/Benchmark/benchmark.exe", "args": ["${workspaceFolder}/tests/benchmark/simple.cpp"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "preLaunchTask": "", "logging": {"moduleLoad": false, "trace": false, "engineLogging": false, "programOutput": true, "exceptions": true}}]}