# Debug Test Projects

This directory contains two test projects to compare debugging behavior between FlashCpp and MSVC compilers.

## Projects

### 1. FlashCppDebugTest
- **Purpose**: Tests debugging with FlashCpp compiler
- **Build Method**: Custom makefile project that uses FlashCpp compiler + MSVC linker
- **Source**: `tests/FlashCppDebugTest/flashcpp_debug_test.cpp`
- **Output**: `Debug/flashcpp_debug_test.exe` and `Debug/flashcpp_debug_test.pdb`

### 2. MSVCDebugTest  
- **Purpose**: Tests debugging with MSVC compiler (reference)
- **Build Method**: Standard Visual Studio C++ project
- **Source**: Same file as FlashCppDebugTest (`tests/FlashCppDebugTest/flashcpp_debug_test.cpp`)
- **Output**: `Debug/msvc_debug_test.exe` and `Debug/msvc_debug_test.pdb`

## How to Test

### Building
1. **FlashCppDebugTest**: 
   - Right-click project → Build
   - Or run `build_flashcpp_debug.bat` manually
   
2. **MSVCDebugTest**:
   - Right-click project → Build
   - Uses standard MSVC compilation

### Debugging Comparison
1. **Set breakpoints** in `flashcpp_debug_test.cpp`:
   - Line 3: `return a + b;` (inside `add` function)
   - Line 8: `return add(3, 5);` (inside `main` function)

2. **Test FlashCpp debugging**:
   - Set FlashCppDebugTest as startup project
   - Press F5 to start debugging
   - Check if breakpoints hit and variables are visible

3. **Test MSVC debugging**:
   - Set MSVCDebugTest as startup project  
   - Press F5 to start debugging
   - Compare debugging experience with FlashCpp

### Expected Results
Both projects should:
- ✅ Build successfully
- ✅ Run and return exit code 8 (3 + 5)
- ✅ Allow setting breakpoints
- ✅ Show local variables (`a`, `b` in `add` function)
- ✅ Allow stepping through code

## Test Source Code
```cpp
int add(int a, int b)
{
return a + b;
}

int main()
{
return add(3, 5);
}
```

## Troubleshooting

### FlashCpp Build Issues
- Ensure FlashCpp.exe is built first (build the main FlashCpp project)
- Check that `build_flashcpp_debug.bat` runs without errors
- Verify MSVC linker paths in the batch file

### Debug Info Issues
- Compare PDB files using `cvdump.exe -sf <pdb_file>`
- Check if source file paths are correct in PDB
- Verify line number information is present

### Visual Studio Issues
- Ensure both projects are in the same solution
- Check that debug information format is set to "Program Database" 
- Verify that optimization is disabled for debug builds
