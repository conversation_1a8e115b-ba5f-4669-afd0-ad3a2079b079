@echo off
echo Building with MSBuild...
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" test.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:OutputFile=call_function_with_argument_ref.obj

echo.
echo Dumping MSBuild's .obj file...
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx86\x86\dumpbin.exe" /all call_function_with_argument_ref.obj

echo.
echo Dumping our .obj file...
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx86\x86\dumpbin.exe" /all call_function_with_argument.obj

echo.
echo Linking call_function_with_argument_ref.obj...
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.38.33130\bin\Hostx64\x64\link.exe" /OUT:call_function_with_argument_ref.exe call_function_with_argument_ref.obj

echo.
echo Linking call_function_with_argument.obj...
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.38.33130\bin\Hostx64\x64\link.exe" /OUT:call_function_with_argument.exe call_function_with_argument.obj

echo.
echo Running call_function_with_argument_ref.exe...
call call_function_with_argument_ref.exe

echo.
echo Running call_function_with_argument.exe...
call call_function_with_argument.exe
