#include <iostream>
#include <vector>
#include <string>

// A simple class to test debugging
class Calculator {
private:
    int value;
    std::string name;

public:
    Calculator(const std::string& n) : value(0), name(n) {
        std::cout << "Calculator " << name << " created" << std::endl;
    }

    int add(int a, int b) {
        int result = a + b;
        value += result;
        return result;
    }

    int multiply(int a, int b) {
        int result = a * b;
        value += result;
        return result;
    }

    int getValue() const {
        return value;
    }

    const std::string& getName() const {
        return name;
    }
};

// Function to test debugging with local variables
int fibonacci(int n) {
    if (n <= 1) {
        return n;
    }
    
    int prev1 = 0;
    int prev2 = 1;
    int current = 0;
    
    for (int i = 2; i <= n; i++) {
        current = prev1 + prev2;
        prev1 = prev2;
        prev2 = current;
    }
    
    return current;
}

// Function to test debugging with arrays and loops
void processArray(int* arr, int size) {
    std::cout << "Processing array of size " << size << std::endl;
    
    for (int i = 0; i < size; i++) {
        arr[i] = arr[i] * 2;
        std::cout << "arr[" << i << "] = " << arr[i] << std::endl;
    }
}

int main() {
    std::cout << "Debug Test Program Starting..." << std::endl;
    
    // Test basic variables
    int x = 10;
    int y = 20;
    int sum = x + y;
    
    std::cout << "x = " << x << ", y = " << y << ", sum = " << sum << std::endl;
    
    // Test class instantiation and method calls
    Calculator calc("TestCalc");
    
    int addResult = calc.add(5, 3);
    int mulResult = calc.multiply(4, 6);
    
    std::cout << "Add result: " << addResult << std::endl;
    std::cout << "Multiply result: " << mulResult << std::endl;
    std::cout << "Calculator total value: " << calc.getValue() << std::endl;
    
    // Test fibonacci function
    int fibNum = 10;
    int fibResult = fibonacci(fibNum);
    std::cout << "Fibonacci(" << fibNum << ") = " << fibResult << std::endl;
    
    // Test array processing
    int testArray[5] = {1, 2, 3, 4, 5};
    processArray(testArray, 5);
    
    // Test vector
    std::vector<int> numbers = {10, 20, 30, 40, 50};
    std::cout << "Vector contents: ";
    for (size_t i = 0; i < numbers.size(); i++) {
        std::cout << numbers[i] << " ";
    }
    std::cout << std::endl;
    
    // Test conditional logic
    if (sum > 25) {
        std::cout << "Sum is greater than 25" << std::endl;
    } else {
        std::cout << "Sum is 25 or less" << std::endl;
    }
    
    // Test while loop
    int counter = 0;
    while (counter < 3) {
        std::cout << "Counter: " << counter << std::endl;
        counter++;
    }
    
    std::cout << "Debug Test Program Completed!" << std::endl;
    return 0;
}
