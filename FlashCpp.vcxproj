<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Benchmark|x64">
      <Configuration>Benchmark</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Test|x64">
      <Configuration>Test</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <ProjectGuid>{3AB9F4D0-719E-42DD-80A0-5297FE82E5AE}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>ClangCL</PlatformToolset>
    <ClangToolsetVersion>19</ClangToolsetVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>ClangCL</PlatformToolset>
    <ClangToolsetVersion>19</ClangToolsetVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Benchmark|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>ClangCL</PlatformToolset>
    <ClangToolsetVersion>19</ClangToolsetVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Test|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>ClangCL</PlatformToolset>
    <ClangToolsetVersion>19</ClangToolsetVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros">
    <LLVMInstallDir>C:\Program Files\LLVM</LLVMInstallDir>
    <BenchmarkOutputFile>$(OutDir)benchmark.exe</BenchmarkOutputFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>$(LLVMInstallDir)\llvm\include;$(LLVMInstallDir)\clang\include;$(LLVMInstallDir)\build\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(LLVMInstallDir)\build\Release\lib;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>$(LLVMInstallDir)\llvm\include;$(LLVMInstallDir)\clang\include;$(LLVMInstallDir)\build\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(LLVMInstallDir)\build\Release\lib;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Benchmark|x64'">
    <IncludePath>$(LLVMInstallDir)\llvm\include;$(LLVMInstallDir)\clang\include;$(LLVMInstallDir)\build\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(LLVMInstallDir)\build\Release\lib;$(LibraryPath)</LibraryPath>
    <TargetName>benchmark</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Test|x64'">
    <IncludePath>$(LLVMInstallDir)\llvm\include;$(LLVMInstallDir)\clang\include;$(LLVMInstallDir)\build\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(LLVMInstallDir)\build\Release\lib;$(LibraryPath)</LibraryPath>
    <TargetName>test</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <Optimization>Disabled</Optimization>
    </ClCompile>
    <Link>
      <TargetMachine>MachineX86</TargetMachine>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <TargetMachine>MachineX86</TargetMachine>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <AdditionalIncludeDirectories>C:\Projects\FlashCpp\external;C:\Projects\FlashCpp\src;C:\Projects\FlashCpp\tests\external\doctest;$(LLVMInstallDir)\include;$(LLVMInstallDir)\lib\clang\19.1.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalOptions>-fms-compatibility -fms-extensions -fdelayed-template-parsing %(AdditionalOptions)</AdditionalOptions>
      <PreprocessorDefinitions>USE_LLVM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <AdditionalIncludeDirectories>C:\Projects\FlashCpp\external;C:\Projects\FlashCpp\src;C:\Projects\FlashCpp\tests\external\doctest;$(LLVMInstallDir)\include;$(LLVMInstallDir)\lib\clang\19.1.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalOptions>-fms-compatibility -fms-extensions -fdelayed-template-parsing %(AdditionalOptions)</AdditionalOptions>
      <PreprocessorDefinitions>USE_LLVM;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Benchmark|x64'">
    <ClCompile>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <AdditionalIncludeDirectories>C:\Projects\FlashCpp\external;C:\Projects\FlashCpp\src;$(LLVMInstallDir)\include;$(LLVMInstallDir)\lib\clang\19.1.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalOptions>-fms-compatibility -fms-extensions -fdelayed-template-parsing %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <OutputFile>$(BenchmarkOutputFile)</OutputFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Test|x64'">
    <ClCompile>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <AdditionalIncludeDirectories>C:\Projects\FlashCpp\external;C:\Projects\FlashCpp\src;C:\Projects\FlashCpp\tests\external\doctest;$(LLVMInstallDir)\include;$(LLVMInstallDir)\lib\clang\19.1.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalOptions>-fms-compatibility -fms-extensions -fdelayed-template-parsing %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <OutputFile>$(OutDir)test.exe</OutputFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="src\AstNodeTypes.cpp" />
    <ClCompile Include="src\ChunkedAnyVector.cpp" />
    <ClCompile Include="src\CodeViewDebug.cpp" />
    <ClCompile Include="src\LibClangIRGenerator_benchmark.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)'!='Benchmark'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="src\Parser.cpp" />
    <!-- Main file - exclude from Test and Benchmark -->
    <ClCompile Include="src\main.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)'=='Benchmark' Or '$(Configuration)'=='Test'">true</ExcludedFromBuild>
    </ClCompile>
    <!-- Benchmark file - only include in Benchmark -->
    <ClCompile Include="src\benchmark.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)'!='Benchmark'">true</ExcludedFromBuild>
    </ClCompile>
    <!-- Test file - only include in Test -->
    <ClCompile Include="tests\FlashCppTest\FlashCppTest\FlashCppTest\FlashCppTest.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)'!='Test'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\ChunkedAnyVector.h" />
    <ClInclude Include="src\CodeGen.h" />
    <ClInclude Include="src\coffi\coffi.hpp" />
    <ClInclude Include="src\coffi\coffi_directory.hpp" />
    <ClInclude Include="src\coffi\coffi_headers.hpp" />
    <ClInclude Include="src\coffi\coffi_relocation.hpp" />
    <ClInclude Include="src\coffi\coffi_section.hpp" />
    <ClInclude Include="src\coffi\coffi_strings.hpp" />
    <ClInclude Include="src\coffi\coffi_symbols.hpp" />
    <ClInclude Include="src\coffi\coffi_types.hpp" />
    <ClInclude Include="src\coffi\coffi_utils.hpp" />
    <ClInclude Include="src\CommandLineParser.h" />
    <ClInclude Include="src\FileReader.h" />
    <ClInclude Include="src\FileTree.h" />
    <ClInclude Include="src\CompileContext.h" />
    <ClInclude Include="src\IRConverter.h" />
    <ClInclude Include="src\IRTypes.h" />
    <ClInclude Include="src\Lexer.h" />
    <ClInclude Include="src\AstNodeTypes.h" />
    <ClInclude Include="src\ObjFileWriter.h" />
    <ClInclude Include="src\Parser.h" />
    <ClInclude Include="src\SymbolTable.h" />
    <ClInclude Include="src\Token.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="LICENSE" />
    <None Include="tests\.runsettings" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>
        $(LLVMInstallDir)\llvm\include;
        $(LLVMInstallDir)\clang\include;
        $(LLVMInstallDir)\build\include;
        %(AdditionalIncludeDirectories)
      </AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <LanguageStandard Condition="'$(Configuration)|$(Platform)'=='Release|x64'">stdcpp20</LanguageStandard>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Test|x64'">Disabled</Optimization>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Link>
      <AdditionalDependencies>ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(LLVMInstallDir)\build\Release\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
    <ClCompile>
      <UndefinePreprocessorDefinitions>USE_LLVM</UndefinePreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Link>
      <AdditionalDependencies>
        ws2_32.lib;
        libclang.lib;
        LLVMCore.lib;
        LLVMSupport.lib;
        LLVMTarget.lib;
        LLVMTargetParser.lib;
        LLVMCodeGen.lib;
        LLVMGlobalISel.lib;
        LLVMSelectionDAG.lib;
        LLVMAsmPrinter.lib;
        LLVMMC.lib;
        LLVMMCParser.lib;
        LLVMBitWriter.lib;
        LLVMBitReader.lib;
        LLVMTransformUtils.lib;
        LLVMAnalysis.lib;
        LLVMIRReader.lib;
        LLVMObject.lib;
        LLVMScalarOpts.lib;
        LLVMInstCombine.lib;
        LLVMInstrumentation.lib;
        LLVMProfileData.lib;
        LLVMCoroutines.lib;
        LLVMDebugInfoDWARF.lib;
        LLVMDebugInfoMSF.lib;
        LLVMDebugInfoCodeView.lib;
        LLVMDebugInfoPDB.lib;
        LLVMRemarks.lib;
        LLVMDemangle.lib;
        LLVMX86CodeGen.lib;
        LLVMX86AsmParser.lib;
        LLVMX86Desc.lib;
        LLVMX86Info.lib;
        LLVMX86Disassembler.lib;
        LLVMX86TargetMCA.lib;
        %(AdditionalDependencies)
      </AdditionalDependencies>
      <AdditionalLibraryDirectories>$(LLVMInstallDir)\build\Release\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
</Project>