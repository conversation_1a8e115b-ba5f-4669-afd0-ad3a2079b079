Microsoft (R) Debugging Information Dumper  Version 14.00.23611
Copyright (C) Microsoft Corporation.  All rights reserved.


***** SECTION #2

*** SYMBOLS

(00000C) S_OBJNAME: Signature: 00000000, C:\Projects\FlashCpp\test_debug_ref.obj

(00003C) S_COMPILE3:
         Language: C++
         Target processor: x64
         Compiled for edit and continue: no
         Compiled without debugging info: no
         Compiled with LTCG: no
         Compiled with /bzalign: no
         Managed code present: no
         Compiled with /GS: yes
         Compiled with /hotpatch: yes
         Converted by CVTCIL: no
         MSIL module: no
         Compiled with /sdl: no
         Compiled with pgo: no
         .EXP module: no
         Pad bits = 0x0000
         Frontend Version: Major = 19, Minor = 44, Build = 35209, QFE = 0
         Backend Version: Major = 19, Minor = 44, Build = 35209, QFE = 0
         Version string: Microsoft (R) Optimizing Compiler

(000078) S_UNAMESPACE: __vc_attributes
(00008C) S_UNAMESPACE: helper_attributes
(0000A2) S_UNAMESPACE: atl
(0000AA) S_UNAMESPACE: std


*** SYMBOLS


(0000BC) S_GPROC32_ID: [0000:00000000], Cb: 00000015, ID:             0x1000, add
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000008, Debug end: 00000014

(0000E7)  S_FRAMEPROC:
          Frame size = 0x00000000 bytes
          Pad size = 0x00000000 bytes
          Offset of pad in frame = 0x00000000
          Size of callee save registers = 0x00000000
          Address of exception handler = 0000:00000000
          Function info: asynceh invalid_pgo_counts opt_for_speed Local=rsp Param=rsp (0x00114200)
(000105)  S_REGREL32: rsp+00000008, Type:       T_INT4(0074), a
(000115)  S_REGREL32: rsp+00000010, Type:       T_INT4(0074), b

(000125) S_PROC_ID_END


*** LINES

  0000:00000000-00000015, flags = 0000, fileid = 00000000

      2 00000000      3 00000008      4 00000014

*** SYMBOLS


(00016C) S_GPROC32_ID: [0000:00000000], Cb: 00000018, ID:             0x1001, main
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000004, Debug end: 00000013

(000198)  S_FRAMEPROC:
          Frame size = 0x00000028 bytes
          Pad size = 0x00000000 bytes
          Offset of pad in frame = 0x00000000
          Size of callee save registers = 0x00000000
          Address of exception handler = 0000:00000000
          Function info: asynceh invalid_pgo_counts opt_for_speed Local=rsp Param=rsp (0x00114200)

(0001B6) S_PROC_ID_END


*** LINES

  0000:00000000-00000018, flags = 0000, fileid = 00000000

      7 00000000      8 00000004      9 00000013

*** FILECHKSUMS

FileId  St.Offset  Cb  Type  ChksumBytes
     0  00000001   20  SHA_256  AF735EC9D792914D046BF7FC2B16B2372BC65E7F7E783F8DA319050FA1FA1F4D

*** SYMBOLS


(00025C) S_BUILDINFO:             0x101B


***** SECTION #6

*** TYPES

0x1000 : Length = 54, Leaf = 0x1515 LF_TYPESERVER2
		GUID={12957EB3-C840-4C50-868D-63ABAB3B38B0}, age = 0x00000001, PDB name = 'C:\Projects\FlashCpp\vc140.pdb

