#pragma once
#include <array>
#include <cstdint>
#include <vector>
#include <unordered_map>
#include <string_view>
#include <cassert>

enum class X64Register : uint8_t {
	RAX, RCX, RDX, RBX, RSP, RBP, RSI, RDI,
	R8, R9, R10, R11, R12, R13, R14, R15,
	XMM0, XMM1, XMM2, XMM3, XMM4, XMM5, XMM6, XMM7,
	XMM8, XMM9, XMM10, XMM11, XMM12, XMM13, XMM14, XMM15,
	Count
};

// Win64 calling convention register mapping
constexpr std::array<X64Register, 4> INT_PARAM_REGS = {
	X64Register::RCX,  // First integer/pointer argument
	X64Register::RDX,  // Second integer/pointer argument
	X64Register::R8,   // Third integer/pointer argument
	X64Register::R9    // Fourth integer/pointer argument
};

constexpr std::array<X64Register, 4> FLOAT_PARAM_REGS = {
	X64Register::XMM0, // First floating point argument
	X64Register::XMM1, // Second floating point argument
	X64Register::XMM2, // Third floating point argument
	X64Register::XMM3  // Fourth floating point argument
};

static X64Register get_int_register_from_arg_index(size_t arg_index) {
	assert(arg_index < INT_PARAM_REGS.size() && "Argument index out of range");
	return INT_PARAM_REGS[arg_index];
}

// ... rest of the file unchanged ... 