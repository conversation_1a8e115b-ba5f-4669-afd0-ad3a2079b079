#include "LibClangIRGenerator.h"
#include "AstNodeTypes.h"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <llvm-c/Core.h>
#include <llvm-c/Target.h>
#include <llvm-c/TargetMachine.h>
#include <llvm-c/Analysis.h>
#include <llvm-c/BitWriter.h>

namespace FlashCpp {

bool GenerateCOFF(const std::vector<ASTNode>& astNodes, const std::string& outputFilename) {
    // Create a new LLVM context and module
    LLVMContextRef context = LLVMContextCreate();
    LLVMModuleRef module = LLVMModuleCreateWithName("benchmark_module");
    LLVMBuilderRef builder = LLVMCreateBuilderInContext(context);

    // Generate LLVM IR from the AST nodes
    for (const auto& node : astNodes) {
        if (node.is<DeclarationNode>()) {
            const DeclarationNode& decl = node.as<DeclarationNode>();
            LLVMTypeRef intType = LLVMIntTypeInContext(context, 32);
            LLVMValueRef globalVariable = LLVMAddGlobal(module, intType, decl.identifier_token().value().data());
            LLVMSetInitializer(globalVariable, LLVMConstInt(intType, 0, 0));
        } else if (node.is<NumericLiteralNode>()) {
            const NumericLiteralNode& literal = node.as<NumericLiteralNode>();
            LLVMTypeRef intType = LLVMIntTypeInContext(context, 32);
            LLVMValueRef constant = LLVMConstInt(intType, std::stoll(literal.token().data()), 0);
        }
    }

    // Initialize target
    LLVMInitializeAllTargetInfos();
    LLVMInitializeAllTargets();
    LLVMInitializeAllTargetMCs();
    LLVMInitializeAllAsmParsers();
    LLVMInitializeAllAsmPrinters();

    // Create target machine
    char* targetTriple = LLVMGetDefaultTargetTriple();
    LLVMTargetRef target;
    char* error = nullptr;
    if (LLVMGetTargetFromTriple(targetTriple, &target, &error)) {
        std::cerr << "Error: Could not get target from triple." << std::endl;
        LLVMDisposeMessage(targetTriple);
        LLVMDisposeBuilder(builder);
        LLVMDisposeModule(module);
        LLVMContextDispose(context);
        return false;
    }

    // Create target machine with minimal options
    LLVMTargetMachineRef targetMachine = LLVMCreateTargetMachine(
        target,
        targetTriple,
        "generic",
        "",
        LLVMCodeGenLevelDefault,
        LLVMRelocDefault,
        LLVMCodeModelDefault);

    LLVMDisposeMessage(targetTriple);

    // Set data layout
    LLVMTargetDataRef dataLayout = LLVMCreateTargetDataLayout(targetMachine);
    LLVMSetModuleDataLayout(module, dataLayout);

    // Write to file
    if (LLVMTargetMachineEmitToFile(targetMachine, module, outputFilename.c_str(), LLVMObjectFile, &error)) {
        std::cerr << "Error: " << error << std::endl;
        LLVMDisposeMessage(error);
        LLVMDisposeBuilder(builder);
        LLVMDisposeModule(module);
        LLVMContextDispose(context);
        LLVMDisposeTargetMachine(targetMachine);
        return false;
    }

    // Cleanup
    LLVMDisposeBuilder(builder);
    LLVMDisposeModule(module);
    LLVMContextDispose(context);
    LLVMDisposeTargetMachine(targetMachine);

    return true;
}

} // namespace FlashCpp 